const [_, wybrany_budynek] = location.search.match(/b=(\w+)/) || [];
const [__, wybrane_pietro] = location.search.match(/p=(\w+)/) || [];

let params = new URLSearchParams(location.search);
let param_budynek = params.get('b');
let param_pietro = params.get('p');

try {
  function close_infoBOX_apartament() {
    document.getElementById("info-popup").style.display = "none";
    document.getElementById("info-popup").classList.remove("status1");
    document.getElementById("info-popup").classList.remove("status2");
  };
  function close_infoBOX_extras() {
    document.getElementById("info-popup").style.display = "none";
    document.getElementById("info-popup").classList.remove("status1");
    document.getElementById("info-popup").classList.remove("status2");
  };

} catch (error) {
  console.error("Error in closePOPUP: ", error);
}

try {
  function open_infoBOX_apartament(id) {
    // Sprawdzenie, czy element "#t" + id istnieje i nie posiada klasy "status3"
    var element = document.querySelector("#t" + id);
    if (!element || element.classList.contains("status3")) {
      return; // Przerwanie funkcji, jeśli element nie istnieje lub posiada klasę "status3"
    }

    var opis = "";
    var price = "";
    const local_type_id = element.getAttribute("data-type-id");
    const typeMapping = {
      "mieszkanie": "Mieszkanie",
      "lokal-uslugowy": "Lokal komercyjny"
  };

  const local_tekst = typeMapping[local_type_id] || "Lokal";



    if (element) {
      var descriptionElement = element.querySelector(".description") || element.querySelector(".system_description");
      if (descriptionElement) {
        opis = descriptionElement.innerHTML;
      }

      var priceElement = element.querySelector(".price");
      if (priceElement) {
        price = "Cena: " + priceElement.innerHTML;
      }
    }

    var infoPopup = document.getElementById("info-popup");
    infoPopup.style.display = "block";
    infoPopup.innerHTML = `<h5><b style='text-align:center'> ${local_tekst} ${id}</b></h5>${opis}<br>${price}`;


    if (element.classList.contains("status1")) {
      infoPopup.classList.add("status1");
    };
    if (element.classList.contains("status2")) {
      infoPopup.classList.add("status2");
    };
  }

  function open_infoBOX_extras(id) {
    // Znajdź element na podstawie id
    const element = document.querySelector("#t" + id);
    const elem_data_extras = document.querySelector("#" + id);
    
    if (!element || element.classList.contains("status3")) {
        console.error("Element nie istnieje lub ma klasę 'status3'.", id);
        return;
    }

    console.log("Element:", element);

    let opis = "";
    let price = "";
    let extra_description = "";
    let komorka_area = "";

    // Pobierz dane z elementu
    const local_data = element.getAttribute("data-local");
    const local_type_id = element.getAttribute("data-type-id");
    const kl_number = elem_data_extras.getAttribute("data-extras")?.trim();

    let element_t_komorka= kl_number? document.querySelector("#t" + kl_number):"";
    if (element){
      var priceElement = element.querySelector(".price");
      if (priceElement) {
        price = "Cena: " + priceElement.innerHTML;
      }
    }

    console.log("Data local:", local_data);
    console.log("Data extras:", kl_number);
    console.log("Type ID:", local_type_id);

    const typeMapping = {
        "miejsce-postojowe-na-zewnatrz": "Miejsce postojowe zewnętrze nr",
        "miejsce-postojowe-garaz": "Miejsce postojowe garażowe nr",
        "komorka-lokatorska": "Komórka lokatorska"
    };

    const parking_text = typeMapping[local_type_id] || "Miejsce";
    const mp_number = local_data ? local_data.trim() : "Brak numeru";

    // Generowanie tekstu
    if (kl_number) {
        text_to_load = `${parking_text} ${mp_number}<br>+ komórka lokatorska ${kl_number}`;
    } else {
        text_to_load = `${parking_text} ${mp_number}`;
    }

    console.log("Text to load:", text_to_load);

    // Pobierz powierzchnię komórki
    const komorkaAreaElement = element_t_komorka?element_t_komorka.getAttribute("data-area"):"";
    if (komorkaAreaElement && parseFloat(komorkaAreaElement) > 0) {
        komorka_area = `Powierzchnia komórki: ${komorkaAreaElement} m<sup>2</sup><br>`;
    }

    // Wyświetl popup
    const infoPopup = document.getElementById("info-popup");
    infoPopup.style.display = "block";
    infoPopup.innerHTML = `<h5><b style='text-align:center'>${text_to_load}</b></h5>${komorka_area}${extra_description}${price}`;

    if (element.classList.contains("status1")) {
        infoPopup.classList.add("status1");
    }
    if (element.classList.contains("status2")) {
        infoPopup.classList.add("status2");
    }
}







} catch (error) {
  console.error("Error in openPOPUP: ", error);
}

try {
  function setPopupPosition(e) {
    let $popup = $("#info-popup");
    if ($popup.length === 0) return;

    let offsetX = 15; // Dodatkowe przesunięcie w poziomie względem kursora

    calculated = document.getElementById('CRM_images').getBoundingClientRect().top + window.scrollY;
    offsetY = 0 ? 400 : calculated; // Stałe przesunięcie w pionie względem kursora, zgodnie z wymaganiami

    // Obliczenie początkowej pozycji popupa
    let posX = e.pageX + offsetX;
    let posY = e.pageY - offsetY; // Użycie stałego offsetu Y

    // Pobranie wymiarów popupa
    let popupWidth = $popup.outerWidth();
    let popupHeight = $popup.outerHeight();

    // Pobranie wymiarów okna przeglądarki
    let windowWidth = $(window).width();
    let windowHeight = $(window).height();
    let scrollY = $(window).scrollTop();

    // Dostosowanie, aby popup nie wychodził poza prawą krawędź okna przeglądarki
    if (posX + popupWidth > windowWidth) {
      posX = e.pageX - popupWidth - offsetX;
    }

    // Dostosowanie, aby popup był zawsze widoczny w obrębie okna przeglądarki
    if (posY < scrollY) {
      posY = scrollY - 200; // Minimalny odstęp od górnej krawędzi okna
    }
    if (posY + popupHeight > scrollY + windowHeight) {
      posY = scrollY + windowHeight - popupHeight - 20; // Ajustowanie do dolnej krawędzi okna
    }

    // Ustawienie obliczonej pozycji popupa
    $popup.css({
      left: posX + "px",
      top: posY + "px"
    });
  }
} catch (error) {
  console.error("Error in setPopupPosition: ", error);
}

try {
  function scrollToElement(selector, offset) {
    var element = jQuery(selector);
    if (element.length) {
      jQuery('html, body').animate({
        scrollTop: element.offset().top - offset
      }, 1000);
    }
  }

} catch (error) {
  console.error("Error in scrollToElement: ", error);
}

try {
  function simulateRadioClick(radioName, radioValue) {
    jQuery("input[type='radio'][name='" + radioName + "'][value='" + radioValue + "']").click();
    if (jQuery("#t" + radioValue).length) {
      scrollToElement("#t" + radioValue, 150);
    } else {
      scrollToElement('#developersystem #controls', 190);
    }
  }

} catch (error) {
  console.error("Error in simulateRadioClick: ", error);
}


function image_apartment_status() {
  // Pobranie elementu #system_content
  var systemContent = document.getElementById('system_content');

  // Sprawdzenie, czy element istnieje na stronie
  if (systemContent) {
    // Pobranie wszystkich bezpośrednich dzieci elementu #system_content
    var children = systemContent.children;

    // Iteracja przez każde dziecko
    for (var i = 0; i < children.length; i++) {
      var child = children[i];

      // Pobranie id i class dla każdego dziecka
      let childId = child.id.slice(1);
      let childClass = child.className;

      // Sprawdzenie, czy klasa zawiera tekst "status"
      if (childClass.includes('status3')) {
        jQuery("#" + childId).toggleClass("aktualne sprzedane");
      }
      if (childClass.includes('status2')) {
        jQuery("#" + childId).toggleClass("aktualne rezerwacja");
      }
    }
  } else {
    console.log('Element #system_content nie został znaleziony.');
  }
}


function sanitizeId(id) {
  // Usunięcie wszystkich znaków niedozwolonych (pozostawienie liter, cyfr, podkreśleń i łączników)
  return id.replace(/[^a-zA-Z0-9_\-]/g, '');
}

function checkAndModifyDuplicateIds() {
  var ids = {};

  // Przechodzimy przez wszystkie elementy wewnątrz #CRM_images mające atrybut id
  $('#CRM_images [id]').each(function () {
    var id = sanitizeId(this.id); // Sanitacja id
    if (ids[id]) {
      // Jeśli id już istnieje, zmień pierwszą literę na 'x' i ustaw zmodyfikowane id
      var newId = 'x' + id.slice(1);
      this.id = newId;
      console.log(`Zmieniono id z ${id} na ${newId} z powodu duplikatu.`);
    } else {
      // Zaznacz, że id zostało już użyte
      ids[id] = true;
    }
  });

  console.log('Sprawdzenie i modyfikacja duplikatów ID zakończone.');
}


// Możesz wywołać tę funkcję, aby sprawdzić i zmodyfikować duplikaty
checkAndModifyDuplicateIds();


try {
  function image_status_actualization(budynekx, pietrox) {
    // Funkcja pomocnicza do aktualizacji stanu
    function aktualizujStan(elementId, stan) {
      let dane = document.getElementById(elementId).textContent;
      let tab = [];
      let i = 0;
      let pietro = [];
      let n = dane.indexOf(";");

      // Znajdź wszystkie indeksy ";"
      while (n > 0) {
        tab.push(n);
        n = dane.indexOf(';', n + 1);
      }

      // Przypisz wartości do tablicy pietro
      tab.forEach((value, index) => {
        pietro[index] = index === 0 ? dane.substr(0, value) : dane.substr(tab[index - 1] + 1, value - tab[index - 1] - 1);
        pietro[index] = pietro[index].replace(";", "").trim();
      });

      // Aktualizuj klasy na podstawie pietro
      pietro.forEach(value => {
        if (value !== "0" && document.querySelector("#p_" + budynekx + "_" + pietrox + " g#" + value) !== null) {
          let element = document.querySelector("#p_" + budynekx + "_" + pietrox + " g#" + value);
          element.classList.add(stan);
          ['rezerwacja', 'sprzedane', 'aktualne'].forEach(cl => {
            if (cl !== stan) element.classList.remove(cl);
          });
        }
      });
    }

    // Aktualizacja dla status_apartamenty-2 (rezerwacja)
    if (document.querySelector("#developer_status #status_2")) {
      aktualizujStan("status_2", 'rezerwacja');
    }

    // Aktualizacja dla status_apartamenty-3 (sprzedane)
    if (document.querySelector("#developer_status #status_3")) {
      aktualizujStan("status_3", 'sprzedane');
    }
  }

} catch (error) {
  console.error("Error in image_status_actualization: ", error);
}

jQuery(document).ready(function () {

  let jQuerypopup = jQuery("#info-popup");
  jQuery(document).on('mousemove', function (e) {
    setPopupPosition(e);
  });


  setTimeout(function () {
    triggerRadioButtonByValue('1', 'status_sterowanie');
  }, 500);


  setTimeout(function () {
    if (param_budynek === null) {
      param_budynek = undefined;
    } else {
      triggerRadioButtonByValue(param_budynek, 'budynek');
    }

    if (param_pietro === null) {
      param_pietro = undefined;
    } else {
      triggerRadioButtonByValue(param_pietro, 'pietro');
    }

    image_apartment_status();
  }, 700);

})
