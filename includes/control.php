<?php

try {
    // Adres URL do nowego REST API
//    $api_url = home_url('/wp-json/dd/v2/realestate/all'); TODO - wersja produkcyjna
    $api_url = 'http://wordpress/wp-json/dd/v2/realestate/all';

    // Pobieranie danych z REST API
    $response = wp_remote_get($api_url, array(
        'timeout' => 30,
        'headers' => array(
            'Accept' => 'application/json',
        ),
    ));

    if (is_wp_error($response)) {
        throw new Exception("Error: Cannot fetch data from API - " . $response->get_error_message());
    }

    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    if (!$data || !isset($data['success']) || !$data['success']) {
        throw new Exception("Error: Invalid API response");
    }

    $real_estate_data = $data['data'];

    function generateSection($items, $fieldName, $label) {
        if (count($items) <= 1) {
            return;
        }

        $output2 = "<div class=\"sort_$fieldName\">";
        $output2 .= "    <div class=\"opis\">$label</div>";
        $output2 .= "    <div class=\"sterowanie\">";
        $output2 .= "        <label>";
        $output2 .= "            <input type=\"radio\" value=\"x\" onclick=\"reload()\" name=\"$fieldName\" checked>";
        $output2 .= "            <div>←</div>";
        $output2 .= "        </label>";

        foreach ($items as $item) {
            if (empty(trim($item)) and trim($item)!=0) {
                continue; // Pomijamy puste wartości
            }
            $output2 .= "<label>";
            $output2 .= "    <input type=\"radio\" value=\"" . htmlspecialchars($item) . "\" onclick=\"reload()\" name=\"$fieldName\">";
            $output2 .= "    <div>" . htmlspecialchars($item) . "</div>";
            $output2 .= "</label>";
        }

        $output2 .= "    </div>";
        $output2 .= "</div>";

        return $output2;
    }

    $buildings = [];
    $floors = [];

    foreach ($real_estate_data as $realestate) {
        $buildings[] = "1"; // Nowe API nie ma pola building, ustawiamy domyślnie na 1
        $floors[] = (string)$realestate['floor'];
    }

    $buildings = array_unique($buildings);
    $floors = array_unique($floors);
    
    sort($buildings);
    sort($floors);

    $output = "<div class=\"sortowanie\"  id=\"controls\">";
    /*$output .= generateSection($buildings, 'budynek', 'BUDYNKI');*/
    $output .= generateSection($floors, 'pietro', 'PIĘTRA');
    $output .= "</div>";

} catch (Exception $e) {
    $output = "<script>console.log('Wyjątek: " . $e->getMessage() . "');</script>";
}
?>
